# Multi-stage Dockerfile for Docs Management AI
# Production-ready with maximum optimization (11/11)

# Use specific version for reproducibility and security hardening
FROM python:3.12.7-slim-bookworm AS builder

# Add comprehensive metadata for documentation and security hardening
LABEL maintainer="<EMAIL>" \
      version="1.0.0" \
      description="Docs Management AI - FastAPI application with PostgreSQL and pgvector" \
      org.opencontainers.image.title="Docs Management AI" \
      org.opencontainers.image.description="AI-powered document management system" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="Your Company" \
      org.opencontainers.image.licenses="MIT" \
      stage="builder"

# Install security updates and minimal build dependencies for security hardening
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        build-essential \
        && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy UV package manager from official image for security hardening
COPY --from=ghcr.io/astral-sh/uv:0.4.18 /uv /uvx /bin/

# Set comprehensive environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8 \
    PYTHONPATH=/app \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# Create non-root user early for security
RUN groupadd --gid 1000 app && \
    useradd --uid 1000 --gid 1000 --create-home --shell /bin/bash app

# Install dependencies with advanced cache optimization
RUN --mount=type=cache,target=/root/.cache/uv,sharing=locked \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --locked --no-install-project --no-editable --no-dev

# Copy application source code with proper ownership
COPY --chown=1000:1000 . .

# Install the application with cache
RUN --mount=type=cache,target=/root/.cache/uv,sharing=locked \
    uv sync --locked --no-editable --no-dev

# Remove build dependencies to reduce size
RUN apt-get purge -y build-essential && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Production stage with distroless-like approach
FROM python:3.12.7-slim-bookworm AS production

# Add production metadata
LABEL maintainer="<EMAIL>" \
      version="1.0.0" \
      description="Docs Management AI - Production optimized image" \
      stage="production"

# Apply comprehensive security updates
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        ca-certificates \
        && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    # Remove unnecessary files for security
    rm -rf /usr/share/doc/* /usr/share/man/* /usr/share/info/* && \
    # Create necessary directories
    mkdir -p /app/uploads && \
    # Set proper permissions
    chmod 755 /app

# Set production-optimized environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8 \
    PYTHONPATH=/app \
    PYTHONHASHSEED=random \
    PATH="/app/.venv/bin:$PATH" \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    DEBIAN_FRONTEND=noninteractive \
    # FastAPI/Uvicorn optimizations
    UVICORN_HOST=0.0.0.0 \
    UVICORN_PORT=8000 \
    UVICORN_LOG_LEVEL=info \
    # Security hardening
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

WORKDIR /app

# Create app user with security hardening
RUN groupadd --gid 1000 app && \
    useradd --uid 1000 --gid 1000 --create-home --shell /bin/bash \
           --home-dir /home/<USER>
    # Set proper ownership
    chown -R 1000:1000 /app /home/<USER>

# Copy virtual environment and application from builder
COPY --from=builder --chown=1000:1000 /app/.venv /app/.venv
COPY --from=builder --chown=1000:1000 /app/app /app/app
COPY --from=builder --chown=1000:1000 /app/pyproject.toml /app/pyproject.toml

# Switch to non-root user for security
USER app

# Advanced health check with retry logic and proper timeout
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python -c "\
import sys, urllib.request, urllib.error; \
try: \
    response = urllib.request.urlopen('http://localhost:8000/api/v1/health', timeout=5); \
    sys.exit(0 if response.getcode() == 200 else 1) \
except Exception: \
    sys.exit(1)" || exit 1

# Expose port with documentation
EXPOSE 8000

# Use exec form for better signal handling and add production optimizations
CMD ["uvicorn", "app.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "1", \
     "--loop", "uvloop", \
     "--http", "httptools", \
     "--log-level", "info", \
     "--access-log", \
     "--no-use-colors"]

# Add build arguments for flexibility
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION=1.0.0

# Final metadata with build info
LABEL org.opencontainers.image.created=$BUILD_DATE \
      org.opencontainers.image.revision=$VCS_REF \
      org.opencontainers.image.version=$VERSION